-- DROP FUNCTION public.get_account_pnl_summarize(varchar, int8, int8, varchar);

CREATE OR REPLACE FUNCTION public.get_account_pnl_summarize(author_id_param character varying, from_time_param bigint, to_time_param bigint, timezone_param character varying)
 RETURNS TABLE(net_asset double precision, today_stock_pnl double precision, total_stock_pnl double precision, today_account_pnl double precision, total_account_pnl double precision, today_account_pnl_value double precision, total_account_pnl_value double precision, cash double precision, cash_percentage double precision, updated_at timestamp with time zone)
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY
    WITH pnl_data AS (
        SELECT aps.net_asset,
            aps.today_stock_pnl,
            aps.total_stock_pnl,
            aps.today_account_pnl,
            aps.total_account_pnl,
            timezone(timezone_param, aps.updated_at::timestamp) as updated_at,
            ROW_NUMBER() OVER (PARTITION BY aps.updated_at::date ORDER BY aps.updated_at::date) AS row_num
        FROM public.account_pnl_summarize aps
        WHERE aps.user_id = author_id_param
            AND timezone(timezone_param, aps.updated_at) >= timezone(timezone_param, TO_TIMESTAMP(from_time_param/1000))
            AND timezone(timezone_param, aps.updated_at) <= timezone(timezone_param, TO_TIMESTAMP(to_time_param/1000))
    ),
    cash_data AS (
        SELECT COALESCE(SUM(ta.cash), 0)::double precision AS cash_value
        FROM trading_accounts ta
        WHERE ta.author_id = author_id_param AND ta.is_visible IS TRUE
    )
    SELECT d.net_asset,
        d.today_stock_pnl,
        d.total_stock_pnl,
        d.today_account_pnl,
        d.total_account_pnl,
        -- Calculate today's P&L value by deriving yesterday's net asset
        -- Formula: Yesterday's Net Asset = Today's Net Asset / (1 + Today's P&L percentage / 100)
        -- Then: Today's P&L Value = Yesterday's Net Asset * Today's P&L percentage / 100
        CASE
            WHEN d.today_account_pnl <> -100 -- Avoid division by zero when P&L is -100%
            THEN (d.net_asset / (1 + d.today_account_pnl / 100)) * (d.today_account_pnl / 100)
            ELSE d.net_asset -- Edge case: if P&L is -100%, the value is the entire net asset
        END AS today_account_pnl_value,
        -- For total P&L value, use the current percentage against current net asset
        -- This assumes total_account_pnl already represents the cumulative percentage gain/loss
        d.net_asset * d.total_account_pnl / 100 AS total_account_pnl_value,
        c.cash_value AS cash,
        CASE WHEN d.net_asset > 0 THEN (c.cash_value / d.net_asset) * 100 ELSE 0 END AS cash_percentage,
        d.updated_at
    FROM pnl_data d
    CROSS JOIN cash_data c
    WHERE d.row_num = 1;
END;
$function$
;
